[{"code": "ai_assistant", "instruction": "You are a helpful AI assistant. Provide clear, accurate, and helpful responses to user questions and requests. Be concise but comprehensive in your answers.", "responseFormat": "text", "chatType": "text", "gptModel": "gpt-4", "inputMessage": "User question or request", "temperature": 0.7, "maxTokens": 2000, "systemMessage": "You are a knowledgeable and helpful AI assistant.", "localization": {"name": {"en": "AI Assistant", "vi": "<PERSON><PERSON><PERSON>"}, "shortName": {"en": "Assistant", "vi": "<PERSON><PERSON><PERSON>"}, "responseHeadline": {"en": "AI Response", "vi": "<PERSON><PERSON><PERSON>"}}}, {"code": "text_summarizer", "instruction": "Summarize the following text into key points. Focus on the main ideas and important details. Present the summary in a clear and organized manner.", "responseFormat": "json_object", "chatType": "text", "gptModel": "gpt-4", "inputMessage": "Text to summarize", "temperature": 0.3, "maxTokens": 1500, "systemMessage": "You are an expert at text summarization and analysis.", "localization": {"name": {"en": "Text Summarizer", "vi": "<PERSON><PERSON><PERSON> tắt văn bản"}, "shortName": {"en": "Summa<PERSON><PERSON>", "vi": "<PERSON><PERSON><PERSON>"}, "responseHeadline": {"en": "Summary", "vi": "<PERSON><PERSON><PERSON>"}}}, {"code": "code_explainer", "instruction": "Analyze and explain the provided code snippet. Include: 1) What the code does, 2) How it works step by step, 3) Key concepts used, 4) Potential improvements or best practices.", "responseFormat": "json_object", "chatType": "text", "gptModel": "gpt-4", "inputMessage": "Code snippet to explain", "temperature": 0.2, "maxTokens": 2500, "systemMessage": "You are an experienced software developer and coding instructor.", "localization": {"name": {"en": "Code Explainer", "vi": "<PERSON><PERSON><PERSON><PERSON> thích mã nguồn"}, "shortName": {"en": "Code Explain", "vi": "<PERSON><PERSON><PERSON><PERSON> thích mã"}, "responseHeadline": {"en": "Code Explanation", "vi": "<PERSON><PERSON><PERSON><PERSON> thích mã nguồn"}}}, {"code": "translator", "instruction": "Translate the provided text accurately while maintaining the original meaning, tone, and context. Provide natural and fluent translation.", "responseFormat": "json_object", "chatType": "text", "gptModel": "gpt-4", "inputMessage": "Text to translate", "temperature": 0.3, "maxTokens": 2000, "systemMessage": "You are a professional translator with expertise in multiple languages.", "localization": {"name": {"en": "AI Translator", "vi": "<PERSON><PERSON><PERSON>"}, "shortName": {"en": "Translator", "vi": "<PERSON><PERSON><PERSON>"}, "responseHeadline": {"en": "Translation", "vi": "<PERSON><PERSON><PERSON>"}}}, {"code": "content_creator", "instruction": "Create engaging and high-quality content based on the user's requirements. Consider the target audience, purpose, and desired tone. Ensure the content is well-structured and compelling.", "responseFormat": "json_object", "chatType": "text", "gptModel": "gpt-4", "inputMessage": "Content requirements and description", "temperature": 0.8, "maxTokens": 3000, "systemMessage": "You are a skilled content creator and copywriter.", "localization": {"name": {"en": "Content Creator", "vi": "<PERSON><PERSON><PERSON> n<PERSON>i dung"}, "shortName": {"en": "Creator", "vi": "<PERSON><PERSON><PERSON> n<PERSON>i dung"}, "responseHeadline": {"en": "Generated Content", "vi": "<PERSON><PERSON><PERSON> dung đ<PERSON><PERSON><PERSON> tạo"}}}, {"code": "text_analyzer", "instruction": "Analyze the provided text for various metrics including word count, readability, sentiment, key themes, and provide insights and recommendations.", "responseFormat": "json_object", "chatType": "text", "gptModel": "gpt-4", "inputMessage": "Text to analyze", "temperature": 0.2, "maxTokens": 2000, "systemMessage": "You are a text analysis expert with knowledge of linguistics and content analysis.", "localization": {"name": {"en": "Text Analyzer", "vi": "<PERSON><PERSON> tích v<PERSON><PERSON> bản"}, "shortName": {"en": "<PERSON><PERSON><PERSON>", "vi": "<PERSON><PERSON> tích"}, "responseHeadline": {"en": "Analysis Results", "vi": "<PERSON><PERSON><PERSON> quả phân tích"}}}]