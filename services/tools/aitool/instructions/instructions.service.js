const DbMongoose = require('../../../../mixins/dbMongo.mixin');
const Model = require('./instructions.model');
const BaseService = require('../../../../mixins/baseService.mixin');
const FunctionsCommon = require('../../../../mixins/functionsCommon.mixin');
const {createMessages} = require('../promptengine');

module.exports = {
  name: 'instructions',
  mixins: [DbMongoose(Model), BaseService, FunctionsCommon],
  settings: {
    entityValidator: {},
    populates: {
      'optionIds': 'options.get',
      'outputTypeId': 'outputtypes.get',
      'subInstructionIds': 'instructions.get',
    },
    populateOptions: ['optionIds', 'outputTypeId', 'subInstructionIds'],
  },

  hooks: {
    before: {},
  },

  actions: {
    details: {
      rest: {
        method: 'GET',
        path: '/:id/details',
      },
      async handler(ctx) {
        const {id, populateOpts} = ctx.params;

        const instruction = await this.adapter.findById(id);

        if (!instruction) return null;
        const populate = populateOpts || ['optionIds', 'outputTypeId', 'subInstructionIds'];
        const transformedInstruction = await this.transformDocuments(ctx, {populate}, instruction);

        return {...transformedInstruction, options: transformedInstruction.optionIds};
      },
    },
    getPrompt: {
      rest: {
        method: 'GET',
        path: '/:id/prompt',
      },
      async handler(ctx) {
        const {id} = ctx.params;

        const instructionData = await this.actions.details({id: id});
        try {
          const messages = await createMessages({
            inputData: {},
            instructionData,
            text: 'INPUT TEXT',
            mockData: 'OPTION VALUE',
          });
          return messages;
        } catch (error) {
          console.log(error);
          return null;
        }
      },
    },

    copy: {
      rest: {
        method: 'POST',
        path: '/copy',
      },
      async handler(ctx) {
        const {instructionId} = ctx.params;

        const instruction = await this.adapter.findById(instructionId);

        if (!instruction) {
          throw new Error(`Instruction with ID ${instructionId} not found`);
        }

        const newInstructionObj = {
          ...instruction.toObject(),
          shortName: `${instruction.shortName} - Copy`,
          _id: undefined,
          createdAt: new Date(),
          updatedAt: new Date(),
          isDeleted: false,
        };

        return this.adapter.insert(newInstructionObj);
      },
    },
    remove: {
      rest: {
        method: 'DELETE',
        path: '/:id',
      },
      async handler(ctx) {
        const {id} = ctx.params;
        ctx.emit('instructionDeleted', {instructionId: id});
        return await this.adapter.updateById(id, {isDeleted: true});
      },
    },

    getAllManager: {
      rest: {
        method: 'GET',
        path: '/manager',
      },
      async handler(ctx) {
        return await this.adapter.find({query: {isDeleted: false}});
      },
    },
  },
  methods: {
    async seedDB() {
      const instructionsDefine = require('./instructions.seed.json');

      await Model.bulkWrite(instructionsDefine.map(row => ({
        updateOne: {
          filter: {code: row.code},
          update: {$set: {...row, isDeleted: false}},
          upsert: true,
        },
      })), {ordered: false});
    },
  },
  events: {},
  async started() {
  },
  async stopped() {
  },
  async afterConnected() {
    const count = await this.adapter.count();
    if (count === 0) {
      return this.seedDB();
    }
  },
};
