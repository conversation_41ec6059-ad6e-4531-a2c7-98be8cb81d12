const DbMongoose = require('../../../../mixins/dbMongo.mixin');
const Model = require('./ouputtypes.model');
const BaseService = require('../../../../mixins/baseService.mixin');
const FunctionsCommon = require('../../../../mixins/functionsCommon.mixin');

module.exports = {
  name: 'outputtypes',
  mixins: [DbMongoose(Model), BaseService, FunctionsCommon],
  settings: {
    /** Validator schema for entity */
    entityValidator: {},
    populates: {},
    populateOptions: [],
  },

  hooks: {
    before: {},
  },

  actions: {},
  methods: {
    async seedDB() {
      const outputTypesDefine = require('./outputtypes.seed.json');

      await Model.bulkWrite(outputTypesDefine.map(row => ({
        updateOne: {
          filter: {code: row.code},
          update: {$set: {...row, isDeleted: false}},
          upsert: true,
        },
      })), {ordered: false});
    },
  },
  events: {},

  async afterConnected() {
    const count = await this.adapter.count();
    if (count === 0) {
      return this.seedDB();
    }
  },
};
