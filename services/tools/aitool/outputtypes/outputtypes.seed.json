[{"code": "text_simple", "responseFormat": "text", "localization": {"name": {"en": "Simple Text", "vi": "<PERSON><PERSON><PERSON> bản đơn giản"}}}, {"code": "text_structured", "responseFormat": "json_object", "schemaInstruction": {"type": "object", "properties": {"title": {"type": "string", "description": "Title of the content"}, "content": {"type": "string", "description": "Main content"}, "summary": {"type": "string", "description": "Brief summary"}}, "required": ["title", "content"]}, "localization": {"name": {"en": "Structured Text", "vi": "<PERSON><PERSON><PERSON> bản có cấu trúc"}}}, {"code": "markdown_content", "responseFormat": "markdown", "localization": {"name": {"en": "Markdown Content", "vi": "<PERSON><PERSON>i dung Markdown"}}}, {"code": "analysis_result", "responseFormat": "json_object", "schemaInstruction": {"type": "object", "properties": {"analysis": {"type": "string", "description": "Detailed analysis"}, "score": {"type": "number", "description": "Analysis score from 0-100"}, "recommendations": {"type": "array", "items": {"type": "string"}, "description": "List of recommendations"}, "categories": {"type": "array", "items": {"type": "string"}, "description": "Relevant categories"}}, "required": ["analysis", "score"]}, "localization": {"name": {"en": "Analysis Result", "vi": "<PERSON><PERSON><PERSON> quả phân tích"}}}, {"code": "translation_result", "responseFormat": "json_object", "schemaInstruction": {"type": "object", "properties": {"originalText": {"type": "string", "description": "Original text"}, "translatedText": {"type": "string", "description": "Translated text"}, "sourceLanguage": {"type": "string", "description": "Source language code"}, "targetLanguage": {"type": "string", "description": "Target language code"}, "confidence": {"type": "number", "description": "Translation confidence score"}}, "required": ["originalText", "translatedText", "sourceLanguage", "targetLanguage"]}, "localization": {"name": {"en": "Translation Result", "vi": "<PERSON><PERSON><PERSON> qu<PERSON> dịch thuật"}}}, {"code": "code_explanation", "responseFormat": "json_object", "schemaInstruction": {"type": "object", "properties": {"explanation": {"type": "string", "description": "Detailed code explanation"}, "language": {"type": "string", "description": "Programming language"}, "complexity": {"type": "string", "enum": ["beginner", "intermediate", "advanced"], "description": "Code complexity level"}, "keyPoints": {"type": "array", "items": {"type": "string"}, "description": "Key points to understand"}, "improvements": {"type": "array", "items": {"type": "string"}, "description": "Suggested improvements"}}, "required": ["explanation", "language"]}, "localization": {"name": {"en": "Code Explanation", "vi": "<PERSON><PERSON><PERSON><PERSON> thích mã nguồn"}}}]