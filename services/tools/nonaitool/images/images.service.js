const sharp = require('sharp');
const {MoleculerClientError} = require('moleculer').Errors;
const i18next = require('i18next');

module.exports = {
  name: 'images',
  mixins: [],
  settings: {
    populates: {},
    populateOptions: [],
  },

  actions: {
    compress: {
      rest: 'POST /compress',
      params: {
        fileId: 'string',
        quality: {type: 'number', optional: true, default: 80},
        format: {type: 'string', optional: true, default: 'jpeg'},
      },
      async handler(ctx) {
        const {fileId, quality, format} = ctx.params;
        
        try {
          const file = await ctx.call('files.get', {id: fileId});
          if (!file) {
            throw new MoleculerClientError(i18next.t('error_file_not_found'), 404);
          }

          const buffer = Buffer.from(file.data, 'base64');
          let compressedBuffer;

          if (format === 'jpeg') {
            compressedBuffer = await sharp(buffer)
              .jpeg({quality})
              .toBuffer();
          } else if (format === 'png') {
            compressedBuffer = await sharp(buffer)
              .png({quality})
              .toBuffer();
          } else if (format === 'webp') {
            compressedBuffer = await sharp(buffer)
              .webp({quality})
              .toBuffer();
          } else {
            throw new MoleculerClientError('Unsupported format', 400);
          }

          const compressedFile = await ctx.call('files.create', {
            filename: `compressed_${file.filename}`,
            mimetype: `image/${format}`,
            data: compressedBuffer.toString('base64'),
          });

          return {
            originalSize: buffer.length,
            compressedSize: compressedBuffer.length,
            compressionRatio: ((buffer.length - compressedBuffer.length) / buffer.length * 100).toFixed(2),
            file: compressedFile,
          };
        } catch (error) {
          throw new MoleculerClientError(`Image compression failed: ${error.message}`, 500);
        }
      },
    },

    convert: {
      rest: 'POST /convert',
      params: {
        fileId: 'string',
        targetFormat: {type: 'string', enum: ['jpeg', 'png', 'webp', 'gif', 'tiff']},
        quality: {type: 'number', optional: true, default: 90},
      },
      async handler(ctx) {
        const {fileId, targetFormat, quality} = ctx.params;
        
        try {
          const file = await ctx.call('files.get', {id: fileId});
          if (!file) {
            throw new MoleculerClientError(i18next.t('error_file_not_found'), 404);
          }

          const buffer = Buffer.from(file.data, 'base64');
          let convertedBuffer;

          const sharpInstance = sharp(buffer);
          
          switch (targetFormat) {
            case 'jpeg':
              convertedBuffer = await sharpInstance.jpeg({quality}).toBuffer();
              break;
            case 'png':
              convertedBuffer = await sharpInstance.png().toBuffer();
              break;
            case 'webp':
              convertedBuffer = await sharpInstance.webp({quality}).toBuffer();
              break;
            case 'gif':
              convertedBuffer = await sharpInstance.gif().toBuffer();
              break;
            case 'tiff':
              convertedBuffer = await sharpInstance.tiff().toBuffer();
              break;
            default:
              throw new MoleculerClientError('Unsupported target format', 400);
          }

          const convertedFile = await ctx.call('files.create', {
            filename: `converted_${file.filename.split('.')[0]}.${targetFormat}`,
            mimetype: `image/${targetFormat}`,
            data: convertedBuffer.toString('base64'),
          });

          return {
            originalFormat: file.mimetype,
            targetFormat: `image/${targetFormat}`,
            file: convertedFile,
          };
        } catch (error) {
          throw new MoleculerClientError(`Image conversion failed: ${error.message}`, 500);
        }
      },
    },

    resize: {
      rest: 'POST /resize',
      params: {
        fileId: 'string',
        width: {type: 'number', optional: true},
        height: {type: 'number', optional: true},
        maintainAspectRatio: {type: 'boolean', optional: true, default: true},
      },
      async handler(ctx) {
        const {fileId, width, height, maintainAspectRatio} = ctx.params;
        
        if (!width && !height) {
          throw new MoleculerClientError('Either width or height must be specified', 400);
        }

        try {
          const file = await ctx.call('files.get', {id: fileId});
          if (!file) {
            throw new MoleculerClientError(i18next.t('error_file_not_found'), 404);
          }

          const buffer = Buffer.from(file.data, 'base64');
          
          let resizeOptions = {};
          if (width) resizeOptions.width = width;
          if (height) resizeOptions.height = height;
          if (!maintainAspectRatio) resizeOptions.fit = 'fill';

          const resizedBuffer = await sharp(buffer)
            .resize(resizeOptions)
            .toBuffer();

          const resizedFile = await ctx.call('files.create', {
            filename: `resized_${file.filename}`,
            mimetype: file.mimetype,
            data: resizedBuffer.toString('base64'),
          });

          return {
            originalDimensions: await this.getImageDimensions(buffer),
            newDimensions: await this.getImageDimensions(resizedBuffer),
            file: resizedFile,
          };
        } catch (error) {
          throw new MoleculerClientError(`Image resize failed: ${error.message}`, 500);
        }
      },
    },

    addBorder: {
      rest: 'POST /add-border',
      params: {
        fileId: 'string',
        borderWidth: {type: 'number', default: 10},
        borderColor: {type: 'string', default: '#000000'},
      },
      async handler(ctx) {
        const {fileId, borderWidth, borderColor} = ctx.params;
        
        try {
          const file = await ctx.call('files.get', {id: fileId});
          if (!file) {
            throw new MoleculerClientError(i18next.t('error_file_not_found'), 404);
          }

          const buffer = Buffer.from(file.data, 'base64');
          
          const borderedBuffer = await sharp(buffer)
            .extend({
              top: borderWidth,
              bottom: borderWidth,
              left: borderWidth,
              right: borderWidth,
              background: borderColor,
            })
            .toBuffer();

          const borderedFile = await ctx.call('files.create', {
            filename: `bordered_${file.filename}`,
            mimetype: file.mimetype,
            data: borderedBuffer.toString('base64'),
          });

          return {
            borderWidth,
            borderColor,
            file: borderedFile,
          };
        } catch (error) {
          throw new MoleculerClientError(`Add border failed: ${error.message}`, 500);
        }
      },
    },
  },

  methods: {
    async getImageDimensions(buffer) {
      const metadata = await sharp(buffer).metadata();
      return {
        width: metadata.width,
        height: metadata.height,
      };
    },
  },

  events: {},
};
